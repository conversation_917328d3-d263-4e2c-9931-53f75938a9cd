"""
Image Plane Module - DICOM PS3.3 C.7.6.2

The Image Plane Module defines the transmitted pixel array of a two dimensional 
image plane in a three dimensional space. This module implements the 
Patient-Based Coordinate System with comprehensive geometric validation.
"""
from .base_module import BaseModule
from ...validators.modules.image_plane_validator import ImagePlaneValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult
from ...utils.dicom_formatters import format_enum_value


class ImagePlaneModule(BaseModule):
    """Image Plane Module implementation for DICOM PS3.3 C.7.6.2.
    
    Uses composition with internal dataset management. Defines the transmitted 
    pixel array of a two dimensional image plane in a three dimensional space
    using the Patient-Based Coordinate System.
    
    Key Features:
    - Enforces paired requirement for Image Position and Image Orientation
    - Validates geometric constraints (orthogonality, normalization)
    - Supports right-handed coordinate system validation
    - Implements coordinate mapping equations (C.7.6.2.1-1 and C.7.6.2.1-2)
    
    Usage:
        # Create with required paired spatial elements
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[0.5, 0.5],
            image_orientation_patient=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            image_position_patient=[100.0, 100.0, 50.0],
            slice_thickness=5.0  # Type 2: required but can be empty
        )
        
        # Add optional spatial elements
        plane.with_optional_elements(
            spacing_between_slices=5.0,
            slice_location=50.0
        )
        
        # Generate dataset for IOD integration
        dataset = plane.to_dataset()
        
        # Validate geometric constraints
        result = plane.validate()
        assert plane.is_geometrically_valid
    """

    @classmethod
    def from_required_elements(
        cls,
        pixel_spacing: list[float],
        image_orientation_patient: list[float],
        image_position_patient: list[float],
        slice_thickness: str | float | int | None = ""
    ) -> 'ImagePlaneModule':
        """Create ImagePlaneModule from all required (Type 1 and Type 2) data elements.
        
        Implements DICOM PS3.3 C.7.6.2 requirements with geometric validation.
        Image Position and Image Orientation must be provided as a pair per standard.
        
        Args:
            pixel_spacing: Physical distance between pixel centers (0028,0030) Type 1
                Format: [row_spacing, column_spacing] in mm. Both values must be positive.
            image_orientation_patient: Direction cosines of first row and column (0020,0037) Type 1
                Format: [row_x, row_y, row_z, col_x, col_y, col_z]. Vectors must be orthogonal and normalized.
            image_position_patient: Upper left corner coordinates (0020,0032) Type 1
                Format: [x, y, z] in mm. Specifies center of first transmitted voxel.
            slice_thickness: Nominal slice thickness in mm (0018,0050) Type 2
                Can be empty string but should be present for spatial completeness.
            
        Returns:
            ImagePlaneModule: New module instance with required spatial elements
            
        Raises:
            ValueError: If geometric constraints are violated (orthogonality, normalization)
            TypeError: If required parameters have incorrect types or dimensions
        """
        instance = cls()
        
        # Validate input parameters before assignment
        cls._validate_pixel_spacing(pixel_spacing)
        cls._validate_orientation_and_position_pair(image_orientation_patient, image_position_patient)
        
        # Assign to internal dataset (composition pattern)
        instance._dataset.PixelSpacing = list(pixel_spacing)  # Ensure list format
        instance._dataset.ImageOrientationPatient = list(image_orientation_patient)
        instance._dataset.ImagePositionPatient = list(image_position_patient)
        
        # Type 2 element: required but can be empty
        if slice_thickness is not None:
            instance._dataset.SliceThickness = format_enum_value(slice_thickness)
        
        return instance
    
    def with_optional_elements(
        self,
        spacing_between_slices: float | None = None,
        slice_location: float | None = None
    ) -> 'ImagePlaneModule':
        """Add optional (Type 3) spatial elements to the image plane.
        
        Args:
            spacing_between_slices: Spacing between adjacent slices in mm (0018,0088) Type 3
                Measured center-to-center. Must not be negative unless specialized IOD defines meaning.
                If provided with slice_thickness, validates logical relationship.
            slice_location: Relative position of image plane in mm (0020,1041) Type 3
                Relative to implementation-specific reference point.
            
        Returns:
            ImagePlaneModule: Self for method chaining
            
        Raises:
            ValueError: If spacing_between_slices is negative (unless specialized IOD)
        """
        if spacing_between_slices is not None:
            if spacing_between_slices < 0:
                raise ValueError(
                    "Spacing Between Slices (0018,0088) must not be negative unless "
                    "specialized IOD defines the meaning of the sign (see DICOM PS3.3 C.7.6.2)"
                )
            self._dataset.SpacingBetweenSlices = float(spacing_between_slices)
            
        if slice_location is not None:
            self._dataset.SliceLocation = float(slice_location)
            
        return self
    
    @property
    def is_orthogonal(self) -> bool:
        """Check if row and column direction cosines are orthogonal.
        
        Per DICOM PS3.3 C.7.6.2: "The row and column direction cosine vectors 
        shall be orthogonal, i.e., their dot product shall be zero."
        
        Returns:
            bool: True if row and column vectors are perpendicular (dot product ≈ 0)
        """
        if not hasattr(self._dataset, 'ImageOrientationPatient'):
            return False
            
        orientation = self._dataset.ImageOrientationPatient
        if not hasattr(orientation, '__len__') or len(orientation) != 6:
            return False
        
        row_cosines = orientation[:3]
        col_cosines = orientation[3:]
        
        try:
            # Calculate dot product
            dot_product = sum(float(r) * float(c) for r, c in zip(row_cosines, col_cosines))
            
            # Check if dot product is approximately zero (orthogonal) - tolerance per DICOM precision
            return abs(dot_product) < 1e-6
        except (ValueError, TypeError):
            return False
    
    @property
    def is_normalized(self) -> bool:
        """Check if row and column direction cosines are normalized (unit vectors).
        
        Per DICOM PS3.3 C.7.6.2: "The row and column direction cosine vectors 
        shall be normal, i.e., the dot product of each direction cosine vector 
        with itself shall be unity."
        
        Returns:
            bool: True if both direction vectors have magnitude 1.0
        """
        if not hasattr(self._dataset, 'ImageOrientationPatient'):
            return False
            
        orientation = self._dataset.ImageOrientationPatient
        if not hasattr(orientation, '__len__') or len(orientation) != 6:
            return False
        
        row_cosines = orientation[:3]
        col_cosines = orientation[3:]
        
        try:
            # Calculate magnitudes (L2 norm)
            row_magnitude = sum(float(r) * float(r) for r in row_cosines) ** 0.5
            col_magnitude = sum(float(c) * float(c) for c in col_cosines) ** 0.5
            
            # Check if magnitudes are approximately 1.0 - tolerance per DICOM precision
            return (abs(row_magnitude - 1.0) < 1e-6 and 
                    abs(col_magnitude - 1.0) < 1e-6)
        except (ValueError, TypeError):
            return False
    
    @property
    def has_valid_spacing_between_slices(self) -> bool:
        """Check if spacing between slices is valid (non-negative).
        
        Per DICOM PS3.3 C.7.6.2: "If present, shall not be negative, unless 
        specialized to define the meaning of the sign in a specialized IOD."
        
        Returns:
            bool: True if spacing is valid or not present
        """
        if not hasattr(self._dataset, 'SpacingBetweenSlices'):
            return True  # Not present is valid (Type 3)
            
        try:
            spacing = float(self._dataset.SpacingBetweenSlices)
            return spacing >= 0
        except (ValueError, TypeError):
            return False
    
    @property
    def pixel_area(self) -> float | None:
        """Calculate pixel area in mm².
        
        Returns:
            float | None: Pixel area in square mm, or None if not determinable
        """
        if not hasattr(self._dataset, 'PixelSpacing'):
            return None
            
        spacing = self._dataset.PixelSpacing
        if not hasattr(spacing, '__len__') or len(spacing) != 2:
            return None
            
        try:
            return float(spacing[0]) * float(spacing[1])
        except (ValueError, TypeError):
            return None
    
    @property
    def voxel_volume(self) -> float | None:
        """Calculate voxel volume in mm³ if slice thickness is available.
        
        Returns:
            float | None: Voxel volume in cubic mm, or None if not determinable
        """
        pixel_area = self.pixel_area
        if pixel_area is None or not hasattr(self._dataset, 'SliceThickness'):
            return None
            
        try:
            thickness = float(self._dataset.SliceThickness)
            if thickness <= 0:
                return None
            return pixel_area * thickness
        except (ValueError, TypeError):
            return None
    
    def get_pixel_coordinates(self, row: int, col: int) -> list[float] | None:
        """Calculate real-world coordinates for a pixel position.
        
        Implements DICOM PS3.3 Equation C.7.6.2.1-1 for mapping integer pixel 
        locations to Patient-Based Coordinate System coordinates.
        
        Args:
            row: Row index (0-based, integer pixel location)
            col: Column index (0-based, integer pixel location)
            
        Returns:
            list[float] | None: [x, y, z] coordinates in mm, or None if calculation fails
        """
        required_attrs = ['ImagePositionPatient', 'ImageOrientationPatient', 'PixelSpacing']
        if not all(hasattr(self._dataset, attr) for attr in required_attrs):
            return None
        
        # Validate attribute dimensions
        position = self._dataset.ImagePositionPatient
        orientation = self._dataset.ImageOrientationPatient
        spacing = self._dataset.PixelSpacing
        
        if (not hasattr(position, '__len__') or len(position) != 3 or
            not hasattr(orientation, '__len__') or len(orientation) != 6 or
            not hasattr(spacing, '__len__') or len(spacing) != 2):
            return None
        
        try:
            # Extract components per DICOM equation C.7.6.2.1-1
            S = [float(x) for x in position]  # Image Position (Patient) - origin
            X = [float(x) for x in orientation[:3]]  # Row direction cosines
            Y = [float(x) for x in orientation[3:]]  # Column direction cosines
            pixel_spacing_row = float(spacing[0])     # Row pixel spacing
            pixel_spacing_col = float(spacing[1])     # Column pixel spacing
            
            # Calculate coordinates using DICOM equation C.7.6.2.1-1:
            # P_xyz = S_xyz + (X_xyz × ΔI × i) + (Y_xyz × ΔJ × j)
            # Where ΔI = column pixel spacing, ΔJ = row pixel spacing
            x = S[0] + X[0] * pixel_spacing_col * col + Y[0] * pixel_spacing_row * row
            y = S[1] + X[1] * pixel_spacing_col * col + Y[1] * pixel_spacing_row * row
            z = S[2] + X[2] * pixel_spacing_col * col + Y[2] * pixel_spacing_row * row
            
            return [x, y, z]
            
        except (ValueError, TypeError):
            return None
    
    @property
    def is_geometrically_valid(self) -> bool:
        """Check if all geometric constraints are satisfied.
        
        Validates orthogonality, normalization, and spatial consistency 
        per DICOM PS3.3 C.7.6.2 requirements.
        
        Returns:
            bool: True if all geometric constraints are satisfied
        """
        return (self.is_orthogonal and 
                self.is_normalized and 
                self.has_valid_spacing_between_slices and
                self.is_right_handed_coordinate_system)
    
    @property
    def is_right_handed_coordinate_system(self) -> bool:
        """Check if coordinate system is right-handed.
        
        Per DICOM PS3.3 C.7.6.2: "The Patient-Based Coordinate System is a 
        right handed system." For image orientation, this means the row and 
        column direction cosines form a valid orthonormal basis consistent
        with a right-handed coordinate system.
        
        Returns:
            bool: True if coordinate system is right-handed
        """
        if not hasattr(self._dataset, 'ImageOrientationPatient'):
            return False
            
        orientation = self._dataset.ImageOrientationPatient
        if not hasattr(orientation, '__len__') or len(orientation) != 6:
            return False
        
        try:
            row_cosines = [float(x) for x in orientation[:3]]
            col_cosines = [float(x) for x in orientation[3:]]
            
            # Calculate cross product: row × column
            cross_product = [
                row_cosines[1] * col_cosines[2] - row_cosines[2] * col_cosines[1],
                row_cosines[2] * col_cosines[0] - row_cosines[0] * col_cosines[2],
                row_cosines[0] * col_cosines[1] - row_cosines[1] * col_cosines[0]
            ]
            
            # Cross product magnitude should be approximately 1.0 for orthogonal unit vectors
            magnitude = sum(x * x for x in cross_product) ** 0.5
            
            # For a valid right-handed coordinate system, the cross product should have unit magnitude
            # The direction of the cross product defines the normal to the image plane
            return abs(magnitude - 1.0) < 1e-6
            
        except (ValueError, TypeError):
            return False
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Image Plane Module instance.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            ValidationResult: ValidationResult with 'errors' and 'warnings' lists
        """
        return ImagePlaneValidator.validate(self._dataset, config)
    
    @staticmethod
    def _validate_pixel_spacing(pixel_spacing: list[float]) -> None:
        """Validate pixel spacing parameter.
        
        Args:
            pixel_spacing: Pixel spacing values to validate
            
        Raises:
            TypeError: If pixel_spacing is not a list/tuple of correct length
            ValueError: If pixel spacing values are not positive
        """
        if not isinstance(pixel_spacing, (list, tuple)) or len(pixel_spacing) != 2:
            raise TypeError(
                "Pixel Spacing (0028,0030) must be a list or tuple of 2 values: [row_spacing, column_spacing]"
            )
        
        try:
            row_spacing, col_spacing = float(pixel_spacing[0]), float(pixel_spacing[1])
            if row_spacing <= 0 or col_spacing <= 0:
                raise ValueError(
                    f"Pixel Spacing (0028,0030) values must be positive: got [{row_spacing}, {col_spacing}]"
                )
        except (ValueError, TypeError) as e:
            raise ValueError(
                f"Pixel Spacing (0028,0030) values must be numeric: {e}"
            )
    
    @staticmethod
    def _validate_orientation_and_position_pair(
        image_orientation_patient: list[float], 
        image_position_patient: list[float]
    ) -> None:
        """Validate image orientation and position as required pair.
        
        Per DICOM PS3.3 C.7.6.2.1.1: "Image Position (Patient) (0020,0032) and 
        Image Orientation (Patient) (0020,0037) shall be provide as a pair."
        
        Args:
            image_orientation_patient: Direction cosines to validate
            image_position_patient: Position coordinates to validate
            
        Raises:
            TypeError: If parameters have incorrect types or dimensions
            ValueError: If geometric constraints are violated
        """
        # Validate Image Orientation (Patient)
        if not isinstance(image_orientation_patient, (list, tuple)) or len(image_orientation_patient) != 6:
            raise TypeError(
                "Image Orientation (Patient) (0020,0037) must be a list or tuple of 6 direction cosine values: "
                "[row_x, row_y, row_z, col_x, col_y, col_z]"
            )
        
        # Validate Image Position (Patient)
        if not isinstance(image_position_patient, (list, tuple)) or len(image_position_patient) != 3:
            raise TypeError(
                "Image Position (Patient) (0020,0032) must be a list or tuple of 3 coordinate values: [x, y, z]"
            )
        
        try:
            # Convert to float and validate geometric constraints
            orientation = [float(x) for x in image_orientation_patient]
            [float(x) for x in image_position_patient]  # Validates numeric conversion
            
            row_cosines = orientation[:3]
            col_cosines = orientation[3:]
            
            # Check orthogonality: dot product should be zero
            dot_product = sum(r * c for r, c in zip(row_cosines, col_cosines))
            if abs(dot_product) > 1e-6:
                raise ValueError(
                    f"Image Orientation (Patient) (0020,0037) row and column direction cosines must be orthogonal. "
                    f"Dot product is {dot_product:.6f}, should be ≈ 0. "
                    f"Check that row and column vectors are perpendicular."
                )
            
            # Check normalization: magnitude should be 1.0
            row_magnitude = sum(r * r for r in row_cosines) ** 0.5
            col_magnitude = sum(c * c for c in col_cosines) ** 0.5
            
            if abs(row_magnitude - 1.0) > 1e-6:
                raise ValueError(
                    f"Image Orientation (Patient) (0020,0037) row direction cosines must be normalized (unit vector). "
                    f"Magnitude is {row_magnitude:.6f}, should be 1.0. "
                    f"Row cosines: {row_cosines}"
                )
            
            if abs(col_magnitude - 1.0) > 1e-6:
                raise ValueError(
                    f"Image Orientation (Patient) (0020,0037) column direction cosines must be normalized (unit vector). "
                    f"Magnitude is {col_magnitude:.6f}, should be 1.0. "
                    f"Column cosines: {col_cosines}"
                )
                
        except (ValueError, TypeError) as e:
            if "must be orthogonal" in str(e) or "must be normalized" in str(e):
                raise  # Re-raise geometric constraint errors with original message
            raise ValueError(
                f"Image Orientation (Patient) (0020,0037) and Image Position (Patient) (0020,0032) "
                f"values must be numeric: {e}"
            )
