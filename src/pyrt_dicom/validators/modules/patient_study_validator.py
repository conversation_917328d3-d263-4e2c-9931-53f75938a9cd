"""Patient Study Module DICOM validation - PS3.3 C.7.2.2

This validator implements comprehensive validation for the Patient Study Module
according to DICOM PS3.3 C.7.2.2 specification, including all conditional
requirements, enumerated values, and sequence structures.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class PatientStudyValidator(BaseValidator):
    """Validator for DICOM Patient Study Module (PS3.3 C.7.2.2).

    Validates all Patient Study Module requirements including:
    - Type 2C conditional requirements for non-human organisms
    - Type 2C conditional requirements for sex parameters
    - Enumerated value constraints
    - Sequence structure requirements
    - Cross-field dependencies and semantic constraints
    """

    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Patient Study Module requirements on any pydicom Dataset.

        Performs comprehensive validation according to DICOM PS3.3 C.7.2.2 including:
        - All Type 2C conditional requirements
        - Enumerated value validation
        - Sequence structure validation
        - Semantic constraint validation
        - Cross-field dependency validation

        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options (defaults to all validations enabled)

        Returns:
            ValidationResult with detailed errors and warnings including DICOM tag references
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate conditional requirements (Type 1C/2C)
        if config.validate_conditional_requirements:
            PatientStudyValidator._validate_conditional_requirements(dataset, result)

        # Validate enumerated values
        if config.check_enumerated_values:
            PatientStudyValidator._validate_enumerated_values(dataset, result)

        # Validate sequence structures
        if config.validate_sequences:
            PatientStudyValidator._validate_sequence_requirements(dataset, result)

        # Validate semantic constraints
        if config.validate_semantic_constraints:
            PatientStudyValidator._validate_semantic_constraints(dataset, result)

        # Validate cross-field dependencies
        if config.validate_cross_field_dependencies:
            PatientStudyValidator._validate_cross_field_dependencies(dataset, result)

        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C and 2C conditional requirements according to PS3.3 C.7.2.2.

        Validates all conditional requirements including:
        - Patient's Sex Neutered (Type 2C) for non-human organisms
        - Sex Parameters Category Comment/Reference (Type 2C) for "Specified" codes
        """

        # Type 2C: Patient's Sex Neutered (0010,2203) required if Patient is a non-human organism
        # Condition: Patient is non-human organism (indicated by species information)
        is_non_human = (hasattr(dataset, 'PatientSpeciesDescription') or
                       hasattr(dataset, 'PatientSpeciesCodeSequence'))

        if is_non_human:
            BaseValidator.validate_conditional_requirement(
                condition=True,
                required_fields=['PatientSexNeutered'],
                dataset=dataset,
                error_message=(
                    "Patient's Sex Neutered (0010,2203) is Type 2C - required when Patient is a "
                    "non-human organism. Detected species information but missing sex neutered status. "
                    "Per DICOM PS3.3 C.7.2.2, this field must be present for non-human organisms."
                ),
                result=result
            )

        # Type 2C: Sex Parameters for Clinical Use Category Comment/Reference required for "Specified" code
        spcu_seq = getattr(dataset, 'SexParametersForClinicalUseCategorySequence', [])
        for i, item in enumerate(spcu_seq):
            code_seq = item.get('SexParametersForClinicalUseCategoryCodeSequence', [])
            for j, code_item in enumerate(code_seq):
                if (code_item.get('CodeValue') == '131232' and
                    code_item.get('CodingSchemeDesignator') == 'DCM'):

                    # Validate Comment (Type 2C)
                    if not item.get('SexParametersForClinicalUseCategoryComment'):
                        result.add_error(
                            f"Sex Parameters for Clinical Use Category Sequence item {i+1}, "
                            f"Code Sequence item {j+1}: Sex Parameters for Clinical Use Category "
                            f"Comment (0010,0042) is Type 2C - required when Category Code is "
                            f"'Specified' (131232, DCM). Per DICOM PS3.3 C.7.2.2, this field "
                            f"must be present to provide clinical context for the specified parameters."
                        )

                    # Validate Reference (Type 2C)
                    if not item.get('SexParametersForClinicalUseCategoryReference'):
                        result.add_error(
                            f"Sex Parameters for Clinical Use Category Sequence item {i+1}, "
                            f"Code Sequence item {j+1}: Sex Parameters for Clinical Use Category "
                            f"Reference (0010,0047) is Type 2C - required when Category Code is "
                            f"'Specified' (131232, DCM). Per DICOM PS3.3 C.7.2.2, this field "
                            f"must provide a reference to the source of the specified parameters."
                        )

    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Smoking Status (0010,21A0)
        smoking_status = getattr(dataset, 'SmokingStatus', '')
        if smoking_status:
            BaseValidator.validate_enumerated_value(
                smoking_status, ["YES", "NO", "UNKNOWN"],
                "Smoking Status (0010,21A0)", result
            )
        
        # Pregnancy Status (0010,21C0)
        pregnancy_status = getattr(dataset, 'PregnancyStatus', '')
        if pregnancy_status:
            BaseValidator.validate_enumerated_value(
                pregnancy_status, ["0001H", "0002H", "0003H", "0004H"],
                "Pregnancy Status (0010,21C0)", result
            )
        
        # Patient's Sex Neutered (0010,2203)
        sex_neutered = getattr(dataset, 'PatientsSexNeutered', '')
        if sex_neutered:
            BaseValidator.validate_enumerated_value(
                sex_neutered, ["ALTERED", "UNALTERED"],
                "Patient's Sex Neutered (0010,2203)", result
            )

    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Gender Identity Sequence validation
        gender_identity_seq = getattr(dataset, 'GenderIdentitySequence', [])
        for i, item in enumerate(gender_identity_seq):
            if not item.get('GenderIdentityCodeSequence'):
                result.add_error(
                    f"Gender Identity Sequence item {i}: "
                    "Gender Identity Code Sequence (0010,0044) is required"
                )
        
        # Sex Parameters for Clinical Use Category Sequence validation
        spcu_seq = getattr(dataset, 'SexParametersForClinicalUseCategorySequence', [])
        for i, item in enumerate(spcu_seq):
            if not item.get('SexParametersForClinicalUseCategoryCodeSequence'):
                result.add_error(
                    f"Sex Parameters for Clinical Use Category Sequence item {i}: "
                    "Sex Parameters for Clinical Use Category Code Sequence (0010,0046) is required"
                )
        
        # Person Names to Use Sequence validation
        names_to_use_seq = getattr(dataset, 'PersonNamesToUseSequence', [])
        for i, item in enumerate(names_to_use_seq):
            if not item.get('NameToUse'):
                result.add_error(
                    f"Person Names to Use Sequence item {i}: "
                    "Name to Use (0010,0012) is required"
                )
        
        # Third Person Pronouns Sequence validation
        pronouns_seq = getattr(dataset, 'ThirdPersonPronounsSequence', [])
        for i, item in enumerate(pronouns_seq):
            if not item.get('PronounCodeSequence'):
                result.add_error(
                    f"Third Person Pronouns Sequence item {i}: "
                    "Pronoun Code Sequence (0010,0015) is required"
                )

